package page

import (
	"testing"

	"halalplus/api/common"
)

func TestNormalizePageRequest(t *testing.T) {
	tests := []struct {
		name     string
		req      *common.PageRequest
		expected *PageParams
	}{
		{
			name: "正常参数",
			req: &common.PageRequest{
				Page: 2,
				Size: 20,
			},
			expected: &PageParams{
				Page: 2,
				Size: 20,
			},
		},
		{
			name: "零值参数",
			req: &common.PageRequest{
				Page: 0,
				Size: 0,
			},
			expected: &PageParams{
				Page: 1,
				Size: 10,
			},
		},
		{
			name: "负值参数",
			req: &common.PageRequest{
				Page: -1,
				Size: -5,
			},
			expected: &PageParams{
				Page: 1,
				Size: 10,
			},
		},
		{
			name: "超大size参数",
			req: &common.PageRequest{
				Page: 1,
				Size: 200,
			},
			expected: &PageParams{
				Page: 1,
				Size: 100, // 被限制为最大值
			},
		},
		{
			name:     "nil请求",
			req:      nil,
			expected: &PageParams{Page: 1, Size: 10},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NormalizePageRequest(tt.req)
			if result.Page != tt.expected.Page || result.Size != tt.expected.Size {
				t.Errorf("NormalizePageRequest() = %+v, want %+v", result, tt.expected)
			}
		})
	}
}

func TestPageParams_ToPageResponse(t *testing.T) {
	params := &PageParams{Page: 2, Size: 10}
	total := 100

	response := params.ToPageResponse(total)

	if response.Page != 2 || response.Size != 10 || response.Total != 100 {
		t.Errorf("ToPageResponse() = %+v, want Page=2, Size=10, Total=100", response)
	}
}

func TestPageParams_GetOffset(t *testing.T) {
	tests := []struct {
		page     int
		size     int
		expected int
	}{
		{1, 10, 0},
		{2, 10, 10},
		{3, 20, 40},
	}

	for _, tt := range tests {
		params := &PageParams{Page: tt.page, Size: tt.size}
		if offset := params.GetOffset(); offset != tt.expected {
			t.Errorf("GetOffset() = %d, want %d", offset, tt.expected)
		}
	}
}

func TestValidateAndNormalize(t *testing.T) {
	tests := []struct {
		name     string
		page     int
		size     int
		expected *PageParams
	}{
		{
			name:     "正常参数",
			page:     2,
			size:     15,
			expected: &PageParams{Page: 2, Size: 15},
		},
		{
			name:     "零值参数",
			page:     0,
			size:     0,
			expected: &PageParams{Page: 1, Size: 10},
		},
		{
			name:     "负值参数",
			page:     -1,
			size:     -5,
			expected: &PageParams{Page: 1, Size: 10},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ValidateAndNormalize(tt.page, tt.size)
			if result.Page != tt.expected.Page || result.Size != tt.expected.Size {
				t.Errorf("ValidateAndNormalize() = %+v, want %+v", result, tt.expected)
			}
		})
	}
}
