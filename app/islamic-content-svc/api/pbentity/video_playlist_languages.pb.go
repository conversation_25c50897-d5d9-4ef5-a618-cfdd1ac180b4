// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: pbentity/video_playlist_languages.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VideoPlaylistLanguages struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                 // 主键ID
	PlaylistId    uint32                 `protobuf:"varint,2,opt,name=PlaylistId,proto3" json:"PlaylistId,omitempty" dc:"播放列表ID"`               // 播放列表ID
	LanguageId    uint32                 `protobuf:"varint,3,opt,name=LanguageId,proto3" json:"LanguageId,omitempty" dc:"语言ID：0-中文，1-英文，2-印尼语"` // 语言ID：0-中文，1-英文，2-印尼语
	Name          string                 `protobuf:"bytes,4,opt,name=Name,proto3" json:"Name,omitempty" dc:"播放列表名称"`                            // 播放列表名称
	ShortTitle    string                 `protobuf:"bytes,5,opt,name=ShortTitle,proto3" json:"ShortTitle,omitempty" dc:"播放列表短标题"`               // 播放列表短标题
	Description   string                 `protobuf:"bytes,6,opt,name=Description,proto3" json:"Description,omitempty" dc:"播放列表描述"`              // 播放列表描述
	CreateTime    uint64                 `protobuf:"varint,7,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间(毫秒时间戳)"`          // 创建时间(毫秒时间戳)
	UpdateTime    uint64                 `protobuf:"varint,8,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间(毫秒时间戳)"`          // 更新时间(毫秒时间戳)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlaylistLanguages) Reset() {
	*x = VideoPlaylistLanguages{}
	mi := &file_pbentity_video_playlist_languages_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlaylistLanguages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlaylistLanguages) ProtoMessage() {}

func (x *VideoPlaylistLanguages) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_video_playlist_languages_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlaylistLanguages.ProtoReflect.Descriptor instead.
func (*VideoPlaylistLanguages) Descriptor() ([]byte, []int) {
	return file_pbentity_video_playlist_languages_proto_rawDescGZIP(), []int{0}
}

func (x *VideoPlaylistLanguages) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VideoPlaylistLanguages) GetPlaylistId() uint32 {
	if x != nil {
		return x.PlaylistId
	}
	return 0
}

func (x *VideoPlaylistLanguages) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *VideoPlaylistLanguages) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VideoPlaylistLanguages) GetShortTitle() string {
	if x != nil {
		return x.ShortTitle
	}
	return ""
}

func (x *VideoPlaylistLanguages) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *VideoPlaylistLanguages) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *VideoPlaylistLanguages) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_video_playlist_languages_proto protoreflect.FileDescriptor

const file_pbentity_video_playlist_languages_proto_rawDesc = "" +
	"\n" +
	"'pbentity/video_playlist_languages.proto\x12\bpbentity\"\xfe\x01\n" +
	"\x16VideoPlaylistLanguages\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x1e\n" +
	"\n" +
	"PlaylistId\x18\x02 \x01(\rR\n" +
	"PlaylistId\x12\x1e\n" +
	"\n" +
	"LanguageId\x18\x03 \x01(\rR\n" +
	"LanguageId\x12\x12\n" +
	"\x04Name\x18\x04 \x01(\tR\x04Name\x12\x1e\n" +
	"\n" +
	"ShortTitle\x18\x05 \x01(\tR\n" +
	"ShortTitle\x12 \n" +
	"\vDescription\x18\x06 \x01(\tR\vDescription\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\a \x01(\x04R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\b \x01(\x04R\n" +
	"UpdateTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_video_playlist_languages_proto_rawDescOnce sync.Once
	file_pbentity_video_playlist_languages_proto_rawDescData []byte
)

func file_pbentity_video_playlist_languages_proto_rawDescGZIP() []byte {
	file_pbentity_video_playlist_languages_proto_rawDescOnce.Do(func() {
		file_pbentity_video_playlist_languages_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_video_playlist_languages_proto_rawDesc), len(file_pbentity_video_playlist_languages_proto_rawDesc)))
	})
	return file_pbentity_video_playlist_languages_proto_rawDescData
}

var file_pbentity_video_playlist_languages_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_video_playlist_languages_proto_goTypes = []any{
	(*VideoPlaylistLanguages)(nil), // 0: pbentity.VideoPlaylistLanguages
}
var file_pbentity_video_playlist_languages_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_video_playlist_languages_proto_init() }
func file_pbentity_video_playlist_languages_proto_init() {
	if File_pbentity_video_playlist_languages_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_video_playlist_languages_proto_rawDesc), len(file_pbentity_video_playlist_languages_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_video_playlist_languages_proto_goTypes,
		DependencyIndexes: file_pbentity_video_playlist_languages_proto_depIdxs,
		MessageInfos:      file_pbentity_video_playlist_languages_proto_msgTypes,
	}.Build()
	File_pbentity_video_playlist_languages_proto = out.File
	file_pbentity_video_playlist_languages_proto_goTypes = nil
	file_pbentity_video_playlist_languages_proto_depIdxs = nil
}
