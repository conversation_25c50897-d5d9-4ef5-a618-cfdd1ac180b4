// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: pbentity/video_playlists.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VideoPlaylists struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                         // 主键ID
	CoverUrl      string                 `protobuf:"bytes,2,opt,name=CoverUrl,proto3" json:"CoverUrl,omitempty" dc:"专题封面图片链接"`          // 专题封面图片链接
	IsVisible     uint32                 `protobuf:"varint,3,opt,name=IsVisible,proto3" json:"IsVisible,omitempty" dc:"是否显示，0-隐藏，1-显示"` // 是否显示，0-隐藏，1-显示
	SortOrder     uint32                 `protobuf:"varint,4,opt,name=SortOrder,proto3" json:"SortOrder,omitempty" dc:"排序权重，数字越小越靠前"`   // 排序权重，数字越小越靠前
	VideoCount    uint32                 `protobuf:"varint,5,opt,name=VideoCount,proto3" json:"VideoCount,omitempty" dc:"播放列表下视频数量"`    // 播放列表下视频数量
	ViewCount     uint64                 `protobuf:"varint,6,opt,name=ViewCount,proto3" json:"ViewCount,omitempty" dc:"播放列表浏览次数"`       // 播放列表浏览次数
	ShareCount    uint64                 `protobuf:"varint,7,opt,name=ShareCount,proto3" json:"ShareCount,omitempty" dc:"播放列表分享次数"`     // 播放列表分享次数
	CollectCount  uint64                 `protobuf:"varint,8,opt,name=CollectCount,proto3" json:"CollectCount,omitempty" dc:"播放列表收藏次数"` // 播放列表收藏次数
	CreateTime    uint64                 `protobuf:"varint,9,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间(毫秒时间戳)"`  // 创建时间(毫秒时间戳)
	UpdateTime    uint64                 `protobuf:"varint,10,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间(毫秒时间戳)"` // 更新时间(毫秒时间戳)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlaylists) Reset() {
	*x = VideoPlaylists{}
	mi := &file_pbentity_video_playlists_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlaylists) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlaylists) ProtoMessage() {}

func (x *VideoPlaylists) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_video_playlists_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlaylists.ProtoReflect.Descriptor instead.
func (*VideoPlaylists) Descriptor() ([]byte, []int) {
	return file_pbentity_video_playlists_proto_rawDescGZIP(), []int{0}
}

func (x *VideoPlaylists) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VideoPlaylists) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *VideoPlaylists) GetIsVisible() uint32 {
	if x != nil {
		return x.IsVisible
	}
	return 0
}

func (x *VideoPlaylists) GetSortOrder() uint32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *VideoPlaylists) GetVideoCount() uint32 {
	if x != nil {
		return x.VideoCount
	}
	return 0
}

func (x *VideoPlaylists) GetViewCount() uint64 {
	if x != nil {
		return x.ViewCount
	}
	return 0
}

func (x *VideoPlaylists) GetShareCount() uint64 {
	if x != nil {
		return x.ShareCount
	}
	return 0
}

func (x *VideoPlaylists) GetCollectCount() uint64 {
	if x != nil {
		return x.CollectCount
	}
	return 0
}

func (x *VideoPlaylists) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *VideoPlaylists) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_video_playlists_proto protoreflect.FileDescriptor

const file_pbentity_video_playlists_proto_rawDesc = "" +
	"\n" +
	"\x1epbentity/video_playlists.proto\x12\bpbentity\"\xba\x02\n" +
	"\x0eVideoPlaylists\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x1a\n" +
	"\bCoverUrl\x18\x02 \x01(\tR\bCoverUrl\x12\x1c\n" +
	"\tIsVisible\x18\x03 \x01(\rR\tIsVisible\x12\x1c\n" +
	"\tSortOrder\x18\x04 \x01(\rR\tSortOrder\x12\x1e\n" +
	"\n" +
	"VideoCount\x18\x05 \x01(\rR\n" +
	"VideoCount\x12\x1c\n" +
	"\tViewCount\x18\x06 \x01(\x04R\tViewCount\x12\x1e\n" +
	"\n" +
	"ShareCount\x18\a \x01(\x04R\n" +
	"ShareCount\x12\"\n" +
	"\fCollectCount\x18\b \x01(\x04R\fCollectCount\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\t \x01(\x04R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\n" +
	" \x01(\x04R\n" +
	"UpdateTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_video_playlists_proto_rawDescOnce sync.Once
	file_pbentity_video_playlists_proto_rawDescData []byte
)

func file_pbentity_video_playlists_proto_rawDescGZIP() []byte {
	file_pbentity_video_playlists_proto_rawDescOnce.Do(func() {
		file_pbentity_video_playlists_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_video_playlists_proto_rawDesc), len(file_pbentity_video_playlists_proto_rawDesc)))
	})
	return file_pbentity_video_playlists_proto_rawDescData
}

var file_pbentity_video_playlists_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_video_playlists_proto_goTypes = []any{
	(*VideoPlaylists)(nil), // 0: pbentity.VideoPlaylists
}
var file_pbentity_video_playlists_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_video_playlists_proto_init() }
func file_pbentity_video_playlists_proto_init() {
	if File_pbentity_video_playlists_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_video_playlists_proto_rawDesc), len(file_pbentity_video_playlists_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_video_playlists_proto_goTypes,
		DependencyIndexes: file_pbentity_video_playlists_proto_depIdxs,
		MessageInfos:      file_pbentity_video_playlists_proto_msgTypes,
	}.Build()
	File_pbentity_video_playlists_proto = out.File
	file_pbentity_video_playlists_proto_goTypes = nil
	file_pbentity_video_playlists_proto_depIdxs = nil
}
